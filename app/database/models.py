"""
Database models for Easy Agent Center.
"""

from typing import Dict, Any
from datetime import datetime

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, JSON, Float
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

Base = declarative_base()


class Agent(Base):
    """Agent model for storing agent configurations."""
    
    __tablename__ = "agents"
    __table_args__ = {'comment': 'Agent 配置表'}
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键 ID')
    agent_id = Column(String(255), unique=True, nullable=False, index=True, comment='Agent 的唯一标识符')
    name = Column(String(255), nullable=False, comment='Agent 名称')
    description = Column(Text, nullable=True, comment='Agent 描述')
    agent_type = Column(String(50), nullable=False, default="react", comment='Agent 类型，例如 react')
    system_prompt = Column(Text, nullable=True, comment='系统提示')
    model = Column(String(255), nullable=False, default="gpt-3.5-turbo", comment='模型名称')
    provider = Column(String(255), nullable=True, comment='LLM 提供商 ID')
    is_default = Column(Boolean, default=False, nullable=False, comment='是否为默认 Agent')
    is_active = Column(Boolean, default=True, nullable=False, comment='是否激活')
    tools = Column(JSON, nullable=True, comment='工具列表 (JSON 数组)')  # Store tools as JSON array
    config = Column(JSON, nullable=True, comment='其他配置 (JSON)')  # Store additional configuration
    llm_config = Column(JSON, nullable=True, comment='LLM 的详细配置')
    created_at = Column(DateTime, default=func.now(), nullable=False, comment='创建时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment='更新时间')
    
    # Note: Relationships removed to avoid foreign key constraints
    # Use service layer methods to query related chat_histories manually
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert agent to dictionary."""
        return {
            "id": self.id,
            "agent_id": self.agent_id,
            "name": self.name,
            "description": self.description,
            "agent_type": self.agent_type,
            "system_prompt": self.system_prompt,
            "model": self.model,
            "provider": self.provider,
            "is_default": self.is_default,
            "is_active": self.is_active,
            "tools": self.tools,
            "config": self.config,
            "llm_config": self.llm_config,
            "created_at": self.created_at.isoformat() if self.created_at is not None else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at is not None else None
        }
    
    def __repr__(self):
        return f"<Agent(agent_id='{self.agent_id}', name='{self.name}')>"


class ChatHistory(Base):
    """Chat history model for storing conversation sessions."""
    
    __tablename__ = "chat_histories"
    __table_args__ = {'comment': '聊天会话历史表'}
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键 ID')
    session_id = Column(String(255), nullable=False, index=True, comment='会话的唯一标识符')
    agent_id = Column(String(255), nullable=False, index=True, comment='关联的 Agent ID')
    title = Column(String(500), nullable=True, comment='可选的会话标题')  # Optional conversation title
    created_at = Column(DateTime, default=func.now(), nullable=False, comment='创建时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment='更新时间')
    
    # Note: Relationships removed to avoid foreign key constraints
    # Use service layer methods to query related agent and messages manually
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert chat history to dictionary."""
        return {
            "id": self.id,
            "session_id": self.session_id,
            "agent_id": self.agent_id,
            "title": self.title,
            "created_at": self.created_at.isoformat() if self.created_at is not None else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at is not None else None,
            "message_count": len(self.messages) if self.messages else 0
        }
    
    def __repr__(self):
        return f"<ChatHistory(session_id='{self.session_id}', agent_id='{self.agent_id}')>"


class ChatMessage(Base):
    """Chat message model for storing individual messages."""
    
    __tablename__ = "chat_messages"
    __table_args__ = {'comment': '聊天消息表'}
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键 ID')
    chat_history_id = Column(Integer, nullable=False, index=True, comment='关联的聊天历史 ID')
    role = Column(String(20), nullable=False, comment='角色 (user, assistant, system)')  # 'user', 'assistant', 'system'
    content = Column(Text, nullable=False, comment='消息内容')
    message_metadata = Column(JSON, nullable=True, comment='其他消息元数据 (JSON)')  # Store additional message metadata
    created_at = Column(DateTime, default=func.now(), nullable=False, comment='创建时间')
    
    # Note: Relationships removed to avoid foreign key constraints
    # Use service layer methods to query related chat_history manually
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert chat message to dictionary."""
        return {
            "id": self.id,
            "chat_history_id": self.chat_history_id,
            "role": self.role,
            "content": self.content,
            "metadata": self.message_metadata,
            "created_at": self.created_at.isoformat() if self.created_at is not None else None
        }
    
    def __repr__(self):
        return f"<ChatMessage(role='{self.role}', content='{self.content[:50]}...')>"


class LLMProvider(Base):
    """LLM Provider model for storing LLM provider configurations."""

    __tablename__ = "llm_providers"
    __table_args__ = {'comment': 'LLM 提供商配置表'}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键 ID')
    provider_id = Column(String(255), unique=True, nullable=False, index=True, comment='提供商的唯一标识符')
    name = Column(String(255), nullable=False, comment='提供商名称')
    description = Column(Text, nullable=True, comment='提供商描述')
    provider_type = Column(String(50), nullable=False, comment='提供商类型 (例如 openai, openai_compatible, azure_openai)')  # openai, openai_compatible, azure_openai, etc.
    api_base = Column(String(500), nullable=True, comment='API 地址')
    api_key = Column(String(500), nullable=True, comment='API 密钥')
    default_model = Column(String(255), nullable=False, default="gpt-3.5-turbo", comment='默认模型')
    context_window = Column(Integer, default=128000, nullable=False, comment='上下文窗口大小')
    max_tokens = Column(Integer, nullable=True, comment='最大令牌数')
    temperature = Column(Float, default=0.7, nullable=False, comment='温度')
    timeout = Column(Integer, default=60, nullable=False, comment='超时时间 (秒)')
    max_retries = Column(Integer, default=3, nullable=False, comment='最大重试次数')
    is_chat_model = Column(Boolean, default=True, nullable=False, comment='是否为聊天模型')
    is_function_calling_model = Column(Boolean, default=False, nullable=False, comment='是否为函数调用模型')
    is_active = Column(Boolean, default=True, nullable=False, comment='是否激活')
    is_default = Column(Boolean, default=False, nullable=False, comment='是否为默认提供商')
    extra_params = Column(JSON, nullable=True, default=list, comment='额外的提供商特定参数 (JSON)')  # Store additional provider-specific parameters
    created_at = Column(DateTime, default=func.now(), nullable=False, comment='创建时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment='更新时间')

    def to_dict(self) -> Dict[str, Any]:
        """Convert LLM provider to dictionary."""
        return {
            "id": self.id,
            "provider_id": self.provider_id,
            "name": self.name,
            "description": self.description,
            "provider_type": self.provider_type,
            "api_base": self.api_base,
            "api_key": self.api_key,  # Note: In production, you might want to mask this
            "default_model": self.default_model,
            "context_window": self.context_window,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
            "is_chat_model": self.is_chat_model,
            "is_function_calling_model": self.is_function_calling_model,
            "is_active": self.is_active,
            "is_default": self.is_default,
            "extra_params": self.extra_params,
            "created_at": self.created_at.isoformat() if self.created_at is not None else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at is not None else None
        }

    def to_dict_safe(self) -> Dict[str, Any]:
        """Convert LLM provider to dictionary without sensitive information."""
        result = self.to_dict()
        # Mask sensitive information
        if result.get("api_key"):
            result["api_key"] = "***masked***"
        return result

    def __repr__(self):
        return f"<LLMProvider(provider_id='{self.provider_id}', name='{self.name}', type='{self.provider_type}')>"


class MCPServer(Base):
    """MCP Server configuration model."""
    __tablename__ = "mcp_servers"
    __table_args__ = {'comment': 'MCP 服务配置表'}

    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键 ID')
    server_id = Column(String(255), nullable=False, index=True, comment='服务器的唯一标识符')
    name = Column(String(255), nullable=False, index=True, comment='服务器名称')
    type = Column(String(50), nullable=False, comment='服务器类型')
    description = Column(Text, nullable=True, comment='服务器描述')
    server_url = Column(String(500), nullable=True, comment='SSE 端点 URL')  # SSE endpoint URL
    command = Column(String(500), nullable=True, comment='启动命令')
    args = Column(JSON, nullable=True, default=list, comment='命令参数 (JSON 列表)')  # List of command arguments
    env = Column(JSON, nullable=True, default=dict, comment='环境变量 (JSON 字典)')  # Environment variables
    is_active = Column(Boolean, default=True, nullable=False, comment='是否激活')
    timeout = Column(Integer, default=30, nullable=False, comment='超时时间 (秒)')
    max_retries = Column(Integer, default=3, nullable=False, comment='最大重试次数')
    extra_config = Column(JSON, nullable=True, comment='额外的配置 (JSON)')
    created_at = Column(DateTime, default=func.now(), nullable=False, comment='创建时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False, comment='更新时间')

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "id": self.id,
            "server_id": self.server_id,
            "name": self.name,
            "type": self.type,
            "description": self.description,
            "server_url": self.server_url,
            "command": self.command,
            "args": self.args or [],
            "env": self.env or {},
            "is_active": self.is_active,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
            "extra_config": self.extra_config,
            "created_at": self.created_at.isoformat() if self.created_at is not None else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at is not None else None
        }

    def to_dict_safe(self) -> Dict[str, Any]:
        """Convert to dictionary without sensitive information."""
        return {
            "id": self.id,
            "server_id": self.server_id,
            "name": self.name,
            "type": self.type,
            "description": self.description,
            "server_url": self.server_url,
            "command": self.command,
            "args": self.args or [],
            "is_active": self.is_active,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
            "extra_config": self.extra_config,
            "created_at": self.created_at.isoformat() if self.created_at is not None else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at is not None else None
        }

    def __repr__(self):
        return f"<MCPServer(server_id='{self.server_id}', name='{self.name}', type='{self.type}', command='{self.command}')>"
