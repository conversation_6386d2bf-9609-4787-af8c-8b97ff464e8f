"""
MCP (Model Context Protocol) Manager for managing MCP servers and tools.
"""

import asyncio
import subprocess
import threading
from typing import Dict, List, Optional
from llama_index.tools.mcp import McpToolSpec
from llama_index.core.tools import BaseTool
from ..logger.logger import get_logger
from .models import MCPServerConfig, MCPServerStatus, MCPTransportType


class MCPManager:
    """
    Manager class for handling MCP servers and their tools.
    
    This class provides functionality to start, stop, and manage MCP servers,
    as well as retrieve tools from connected servers.
    """
    
    def __init__(self):
        """Initialize the MCP manager."""
        self._servers: Dict[str, MCPServerConfig] = {}
        self._server_processes: Dict[str, subprocess.Popen] = {}
        self._server_tools: Dict[str, List[BaseTool]] = {}
        self._server_status: Dict[str, MCPServerStatus] = {}
        self._mcp_tool_specs: Dict[str, McpToolSpec] = {}
        self._lock = threading.RLock()
        
        # Initialize logger
        self.logger = get_logger("mcp.MCPManager")
        self.logger.info("🚀 MCPManager initialized")
        
        # Load and start all active servers from the database
        asyncio.create_task(self.load_and_start_servers())

    async def load_and_start_servers(self) -> None:
        """
        Load all active MCP servers from the database and start them.
        """
        from app.database.services import MCPService
        from app.database.connection import get_database_manager
        
        self.logger.info("🔄 Loading and starting all active MCP servers...")
        
        # Wait for the database to be initialized
        db_manager = get_database_manager()
        for _ in range(30):  # Wait for up to 30 seconds
            if db_manager.is_initialized:
                break
            await asyncio.sleep(1)
        else:
            self.logger.error("Database not initialized after 30 seconds. Aborting server load.")
            return

        try:
            servers = await MCPService.list_mcp_servers(True)
            
            for server in servers:
                server_dict = server.to_dict()
                config = MCPServerConfig(**server_dict)
                await self.add_server(config)
                    
            self.logger.info(f"✅ Loaded and started {len(servers)} active MCP servers")
            
        except Exception as e:
            self.logger.error("Failed to load and start MCP servers", exception=e)

    async def add_server(self, config: MCPServerConfig) -> None:
        """
        Add a new MCP server configuration.
        
        Args:
            config: MCP server configuration
        """
        try:
            with self._lock:
                if config.server_id in self._servers:
                    raise ValueError(f"MCP server '{config.server_id}' already exists")
                
                self._servers[config.server_id] = config
                self._server_status[config.server_id] = MCPServerStatus(
                    server_id=config.server_id,
                    name=config.name,
                    is_running=False,
                    is_connected=False
                )
                
                self.logger.info(f"📡 Added MCP server: {config.server_id}")
                
                await self.start_server(config.server_id)
                    
        except Exception as e:
            self.logger.error(f"Failed to add MCP server '{config.server_id}'", exception=e)
            raise
    
    def remove_server(self, server_id: str) -> bool:
        """
        Remove an MCP server.
        
        Args:
            server_id: The server ID to remove
            
        Returns:
            bool: True if server was removed, False if not found
        """
        try:
            with self._lock:
                if server_id not in self._servers:
                    self.logger.warning(f"MCP server '{server_id}' not found for removal")
                    return False
                
                # Stop the server if running
                asyncio.create_task(self.stop_server(server_id))
                
                # Remove from all collections
                del self._servers[server_id]
                self._server_tools.pop(server_id, None)
                self._server_status.pop(server_id, None)
                self._mcp_tool_specs.pop(server_id, None)
                
                self.logger.info(f"🗑️ Removed MCP server: {server_id}")
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to remove MCP server '{server_id}'", exception=e)
            raise
    
    async def start_server(self, server_id: str) -> bool:
        """
        Start an MCP server.
        
        Args:
            server_id: The server ID to start
            
        Returns:
            bool: True if server started successfully
        """
        try:
            with self._lock:
                if server_id not in self._servers:
                    self.logger.error(f"MCP server '{server_id}' not found")
                    return False
                
                config = self._servers[server_id]
                status = self._server_status[server_id]
                
                # if config.server_url:
                # if config.type == MCPTransportType.SSE and config.server_url:
                await self._connect_and_load_tools(server_id)
                return True

                # if status.is_running:
                #     self.logger.warning(f"MCP server '{server_id}' is already running")
                #     return True

                # if not config.command:
                #     error_msg = f"MCP server '{server_id}' has no command configured to start."
                #     self.logger.error(error_msg)
                #     status.last_error = error_msg
                #     return False

                # # Check if command exists
                # import shutil
                # if not shutil.which(config.command):
                #     error_msg = f"Command '{config.command}' not found. Please ensure it is installed and in your PATH."
                #     self.logger.error(error_msg)
                #     status.last_error = error_msg
                #     return False
                
                # self.logger.info(f"🚀 Starting MCP server: {server_id}")
                
                # # Construct the command list
                # command_list = [config.command]
                # command_list.extend(config.args or [])

                # # Start the server process
                # import os
                # env = os.environ.copy()
                # if config.env:
                #     env.update(config.env)
                    
                # process = subprocess.Popen(
                #     command_list,
                #     env=env,
                #     stdout=subprocess.PIPE,
                #     stderr=subprocess.PIPE,
                #     text=True
                # )
                
                # self._server_processes[server_id] = process
                
                # # Wait a moment for the server to start
                # await asyncio.sleep(1)
                
                # # Check if process is still running
                # if process.poll() is None:
                #     status.is_running = True
                #     self.logger.info(f"✅ MCP server '{server_id}' started successfully")
                    
                #     # Try to connect and load tools
                #     if config.server_url:
                #         await self._connect_and_load_tools(server_id, config.server_url)
                #     else:
                #         self.logger.warning(f"MCP server '{server_id}' started but no server_url is configured for connection.")
                #     return True
                # else:
                #     _, stderr = process.communicate()
                #     error_msg = f"MCP server '{server_id}' failed to start. stderr: {stderr}"
                #     status.last_error = error_msg
                #     self.logger.error(error_msg)
                #     return False
                    
        except Exception as e:
            self.logger.error(f"Failed to start MCP server '{server_id}'", exception=e)
            if server_id in self._server_status:
                self._server_status[server_id].last_error = str(e)
            return False
    
    async def stop_server(self, server_id: str) -> bool:
        """
        Stop an MCP server.
        
        Args:
            server_id: The server ID to stop
            
        Returns:
            bool: True if server stopped successfully
        """
        try:
            with self._lock:
                if server_id not in self._servers:
                    self.logger.error(f"MCP server '{server_id}' not found")
                    return False
                
                status = self._server_status[server_id]
                
                if not status.is_running:
                    self.logger.warning(f"MCP server '{server_id}' is not running")
                    return True
                
                self.logger.info(f"🛑 Stopping MCP server: {server_id}")
                
                # Stop the process
                if server_id in self._server_processes:
                    process = self._server_processes[server_id]
                    process.terminate()
                    
                    # Wait for graceful shutdown
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        process.kill()
                        process.wait()
                    
                    del self._server_processes[server_id]
                
                # Update status
                status.is_running = False
                status.is_connected = False
                
                # Clear tools
                self._server_tools.pop(server_id, None)
                self._mcp_tool_specs.pop(server_id, None)
                
                self.logger.info(f"✅ MCP server '{server_id}' stopped successfully")
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to stop MCP server '{server_id}'", exception=e)
            return False

    async def _connect_and_load_tools(self, server_id: str) -> bool:
        """
        Connect to an MCP server and load its tools.

        Args:
            server_id: The server ID to connect to

        Returns:
            bool: True if connection and tool loading succeeded
        """
        try:
            config = self._servers[server_id]  # TODO: Use for proper MCP client configuration
            status = self._server_status[server_id]

            self.logger.info(f"🔌 Connecting to MCP server: {server_id}")

            # Create McpToolSpec for this server
            # Note: This is a simplified approach - in practice, you might need
            # to configure the connection based on the server type and config
            from llama_index.tools.mcp.client import BasicMCPClient
            from .models import MCPTransportType

            mcp_client = None
            if config.type == MCPTransportType.STDIO:
                if not config.command:
                    raise ValueError("command is required for STDIO transport type")
                mcp_client = BasicMCPClient(
                    command_or_url=config.command,
                    args=config.args,
                    env=config.env,
                    timeout=config.timeout,
                )
            elif config.type in [MCPTransportType.SSE, MCPTransportType.HTTP]:
                if not config.server_url:
                    raise ValueError(
                        f"server_url is required for {config.type.value} transport type"
                    )
                mcp_client = BasicMCPClient(
                    command_or_url=config.server_url, timeout=config.timeout
                )
            else:
                raise ValueError(f"Unsupported MCP transport type: {config.type}")

            mcp_tool_spec = McpToolSpec(client=mcp_client)

            # Load tools from the server
            tools = await mcp_tool_spec.to_tool_list_async()

            # Store the tools and tool spec (cast to List[BaseTool] for type compatibility)
            self._server_tools[server_id] = list(tools)
            self._mcp_tool_specs[server_id] = mcp_tool_spec

            # Update status
            status.is_connected = True
            status.tool_count = len(tools)
            status.last_error = None

            self.logger.info(f"✅ Connected to MCP server '{server_id}' and loaded {len(tools)} tools")
            return True

        except Exception as e:
            error_msg = f"Failed to connect to MCP server '{server_id}': {str(e)}"
            self.logger.error(error_msg, exception=e)
            if server_id in self._server_status:
                self._server_status[server_id].last_error = error_msg
                self._server_status[server_id].is_connected = False
            return False

    def get_server_tools(self, server_id: str) -> List[BaseTool]:
        """
        Get tools from a specific MCP server.

        Args:
            server_id: The server ID

        Returns:
            List[BaseTool]: List of tools from the server
        """
        with self._lock:
            return self._server_tools.get(server_id, [])

    def get_all_tools(self) -> List[BaseTool]:
        """
        Get all tools from all connected MCP servers.

        Returns:
            List[BaseTool]: List of all available MCP tools
        """
        with self._lock:
            all_tools = []
            for tools in self._server_tools.values():
                all_tools.extend(tools)
            return all_tools

    def get_server_status(self, server_id: str) -> Optional[MCPServerStatus]:
        """
        Get status of a specific MCP server.

        Args:
            server_id: The server ID

        Returns:
            Optional[MCPServerStatus]: Server status or None if not found
        """
        with self._lock:
            return self._server_status.get(server_id)

    def list_servers(self) -> Dict[str, MCPServerStatus]:
        """
        List all MCP servers and their status.

        Returns:
            Dict[str, MCPServerStatus]: Dictionary of server statuses
        """
        with self._lock:
            return self._server_status.copy()

    def get_server_config(self, server_id: str) -> Optional[MCPServerConfig]:
        """
        Get configuration of a specific MCP server.

        Args:
            server_id: The server ID

        Returns:
            Optional[MCPServerConfig]: Server configuration or None if not found
        """
        with self._lock:
            return self._servers.get(server_id)

    async def restart_server(self, server_id: str) -> bool:
        """
        Restart an MCP server.

        Args:
            server_id: The server ID to restart

        Returns:
            bool: True if server restarted successfully
        """
        self.logger.info(f"🔄 Restarting MCP server: {server_id}")

        # Stop the server
        stop_success = await self.stop_server(server_id)
        if not stop_success:
            return False

        # Wait a moment
        await asyncio.sleep(1)

        # Start the server
        return await self.start_server(server_id)

    async def shutdown_all(self) -> None:
        """Shutdown all MCP servers."""
        self.logger.info("🛑 Shutting down all MCP servers")

        with self._lock:
            server_ids = list(self._servers.keys())

        # Stop all servers concurrently
        tasks = [self.stop_server(server_id) for server_id in server_ids]
        await asyncio.gather(*tasks, return_exceptions=True)

        self.logger.info("✅ All MCP servers shut down")


# Global MCP manager instance
mcp_manager = MCPManager()
