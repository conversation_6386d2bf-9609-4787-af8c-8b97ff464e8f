"""
MCP (模型上下文协议) API 端点。
"""

import secrets
import string
from typing import List
from fastapi import APIRouter, HTTPException
from ..logger.logger import get_logger, PerformanceLogger
from ..database.services import MCPService
from .models import (
    CreateMCPServerRequest,
    UpdateMCPServerRequest,
    MCPServerSafeResponse,
    MCPServerDeleteResponse,
    MCPToolResponse,
    MCPServerStatus
)
from .mcp_manager import mcp_manager

# 创建路由器
router = APIRouter(prefix="/mcp", tags=["MCP 服务管理"])

# 初始化日志记录器
api_logger = get_logger("mcp.api")


@router.post("/servers", response_model=MCPServerSafeResponse, summary="创建新的 MCP 服务器")
async def create_mcp_server(request: CreateMCPServerRequest):
    """创建一个新的 MCP 服务器。"""
    try:
        with PerformanceLogger(f"api_create_mcp_server_{request.name}", api_logger):
            # 生成一个随机的 server_id
            alphabet = string.ascii_letters + string.digits
            server_id = ''.join(secrets.choice(alphabet) for _ in range(32))

            server = await MCPService.create_mcp_server(
                server_id=server_id,
                name=request.name,
                description=request.description,
                command=request.command,
                args=request.args,
                env=request.env,
                type=request.type.value,
                server_url=request.serverUrl,
                is_active=request.isActive,
                timeout=request.timeout,
                max_retries=request.maxRetries,
                extra_config=request.extraConfig
            )

            # 将服务器添加到 MCP 管理器
            from .models import MCPServerConfig
            config = MCPServerConfig(
                server_id=str(server.server_id),
                name=request.name,
                description=request.description,
                command=request.command,
                args=request.args,
                env=request.env,
                type=request.type,
                server_url=request.serverUrl,
                is_active=request.isActive,
                timeout=request.timeout,
                max_retries=request.maxRetries,
                extra_config=request.extraConfig
            )
            await mcp_manager.add_server(config)

            api_logger.info(f"已创建 MCP 服务器: {request.name}")
            return MCPServerSafeResponse(**server.to_dict_safe())
    except ValueError as e:
        api_logger.warning(f"MCP 服务器创建失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        api_logger.error(f"为 {request.name} 创建 MCP 服务器失败", exception=e)
        raise HTTPException(status_code=500, detail=f"创建 MCP 服务器时出错: {str(e)}")


@router.get("/servers", response_model=List[MCPServerSafeResponse], summary="列出所有 MCP 服务器")
async def list_mcp_servers():
    """列出所有 MCP 服务器。"""
    try:
        with PerformanceLogger("api_list_mcp_servers", api_logger):
            servers = await MCPService.list_mcp_servers()
            api_logger.info(f"已列出 {len(servers)} 个 MCP 服务器")
            return [MCPServerSafeResponse(**server.to_dict_safe()) for server in servers]
    except Exception as e:
        api_logger.error("无法列出 MCP 服务器", exception=e)
        raise HTTPException(status_code=500, detail=f"列出 MCP 服务器时出错: {str(e)}")


@router.get("/servers/{server_id}", response_model=MCPServerSafeResponse, summary="获取特定的 MCP 服务器")
async def get_mcp_server(server_id: str):
    """获取一个特定的 MCP 服务器。"""
    try:
        with PerformanceLogger(f"api_get_mcp_server_{server_id}", api_logger):
            server = await MCPService.get_mcp_server_by_id(server_id)
            if not server:
                raise HTTPException(status_code=404, detail=f"未找到 MCP 服务器 '{server_id}'")
            
            api_logger.info(f"已检索到 MCP 服务器: {server_id}")
            return MCPServerSafeResponse(**server.to_dict_safe())
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"无法获取 MCP 服务器 {server_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"获取 MCP 服务器时出错: {str(e)}")


@router.put("/servers/{server_id}", response_model=MCPServerSafeResponse, summary="更新 MCP 服务器")
async def update_mcp_server(server_id: str, request: UpdateMCPServerRequest):
    """更新一个 MCP 服务器。"""
    try:
        with PerformanceLogger(f"api_update_mcp_server_{server_id}", api_logger):
            server = await MCPService.update_mcp_server(
                server_id=server_id,
                name=request.name,
                description=request.description,
                command=request.command,
                args=request.args,
                env=request.env,
                type=request.type.value if request.type else None,
                server_url=request.server_url,
                is_active=request.is_active,
                timeout=request.timeout,
                max_retries=request.max_retries,
                extra_config=request.extra_config
            )
            
            if not server:
                raise HTTPException(status_code=404, detail=f"未找到 MCP 服务器 '{server_id}'")
            
            api_logger.info(f"已更新 MCP 服务器: {server_id}")
            return MCPServerSafeResponse(**server.to_dict_safe())
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"无法更新 MCP 服务器 {server_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"更新 MCP 服务器时出错: {str(e)}")


@router.delete("/servers/{server_id}", response_model=MCPServerDeleteResponse, summary="删除 MCP 服务器")
async def delete_mcp_server(server_id: str):
    """删除一个 MCP 服务器。"""
    try:
        with PerformanceLogger(f"api_delete_mcp_server_{server_id}", api_logger):
            success = await MCPService.delete_mcp_server(server_id)
            if not success:
                raise HTTPException(status_code=404, detail=f"未找到 MCP 服务器 '{server_id}'")
            
            # 从 MCP 管理器中移除
            mcp_manager.remove_server(server_id)
            
            api_logger.info(f"已删除 MCP 服务器: {server_id}")
            return MCPServerDeleteResponse(
                message=f"MCP 服务器 '{server_id}' 已成功删除",
                server_id=server_id
            )
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"无法删除 MCP 服务器 {server_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"删除 MCP 服务器时出错: {str(e)}")


@router.post("/servers/{server_id}/start", summary="启动 MCP 服务器")
async def start_mcp_server(server_id: str):
    """启动一个 MCP 服务器。"""
    try:
        with PerformanceLogger(f"api_start_mcp_server_{server_id}", api_logger):
            success = await mcp_manager.start_server(server_id)
            if not success:
                raise HTTPException(status_code=400, detail=f"无法启动 MCP 服务器 '{server_id}'")
            
            api_logger.info(f"已启动 MCP 服务器: {server_id}")
            return {"message": f"MCP 服务器 '{server_id}' 已成功启动", "server_id": server_id}
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"无法启动 MCP 服务器 {server_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"启动 MCP 服务器时出错: {str(e)}")


@router.post("/servers/{server_id}/stop", summary="停止 MCP 服务器")
async def stop_mcp_server(server_id: str):
    """停止一个 MCP 服务器。"""
    try:
        with PerformanceLogger(f"api_stop_mcp_server_{server_id}", api_logger):
            success = await mcp_manager.stop_server(server_id)
            if not success:
                raise HTTPException(status_code=400, detail=f"无法停止 MCP 服务器 '{server_id}'")
            
            api_logger.info(f"已停止 MCP 服务器: {server_id}")
            return {"message": f"MCP 服务器 '{server_id}' 已成功停止", "server_id": server_id}
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"无法停止 MCP 服务器 {server_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"停止 MCP 服务器时出错: {str(e)}")


@router.post("/servers/{server_id}/restart", summary="重启 MCP 服务器")
async def restart_mcp_server(server_id: str):
    """重启一个 MCP 服务器。"""
    try:
        with PerformanceLogger(f"api_restart_mcp_server_{server_id}", api_logger):
            success = await mcp_manager.restart_server(server_id)
            if not success:
                raise HTTPException(status_code=400, detail=f"无法重启 MCP 服务器 '{server_id}'")
            
            api_logger.info(f"已重启 MCP 服务器: {server_id}")
            return {"message": f"MCP 服务器 '{server_id}' 已成功重启", "server_id": server_id}
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"无法重启 MCP 服务器 {server_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"重启 MCP 服务器时出错: {str(e)}")


@router.get("/servers/{server_id}/status", response_model=MCPServerStatus, summary="获取 MCP 服务器状态")
async def get_mcp_server_status(server_id: str):
    """获取一个 MCP 服务器的状态。"""
    try:
        with PerformanceLogger(f"api_get_mcp_server_status_{server_id}", api_logger):
            status = mcp_manager.get_server_status(server_id)
            if not status:
                raise HTTPException(status_code=404, detail=f"未找到 MCP 服务器 '{server_id}'")
            
            api_logger.info(f"已检索到 MCP 服务器状态: {server_id}")
            return status
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"无法获取 MCP 服务器状态 {server_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"获取 MCP 服务器状态时出错: {str(e)}")


@router.get("/servers/{server_id}/tools", response_model=MCPToolResponse, summary="获取 MCP 服务器的工具")
async def get_mcp_server_tools(server_id: str):
    """从一个 MCP 服务器获取工具。"""
    try:
        with PerformanceLogger(f"api_get_mcp_server_tools_{server_id}", api_logger):
            tools = mcp_manager.get_server_tools(server_id)
            server_config = mcp_manager.get_server_config(server_id)
            
            if not server_config:
                raise HTTPException(status_code=404, detail=f"未找到 MCP 服务器 '{server_id}'")
            
            # 将工具转换为字典格式
            tool_dicts = []
            for tool in tools:
                if hasattr(tool, 'metadata') and tool.metadata:
                    tool_dicts.append({
                        "name": tool.metadata.name,
                        "description": tool.metadata.description,
                        "parameters": getattr(tool.metadata, 'fn_schema', {})
                    })
            
            api_logger.info(f"从 MCP 服务器检索到 {len(tool_dicts)} 个工具: {server_id}")
            return MCPToolResponse(
                server_id=server_id,
                server_name=server_config.name,
                tools=tool_dicts
            )
    except HTTPException:
        raise
    except Exception as e:
        api_logger.error(f"无法获取 MCP 服务器工具 {server_id}", exception=e)
        raise HTTPException(status_code=500, detail=f"获取 MCP 服务器工具时出错: {str(e)}")


@router.get("/tools", response_model=List[MCPToolResponse], summary="获取所有 MCP 服务器的工具")
async def get_all_mcp_tools():
    """从所有 MCP 服务器获取所有工具。"""
    try:
        with PerformanceLogger("api_get_all_mcp_tools", api_logger):
            all_servers = mcp_manager.list_servers()
            tool_responses = []
            
            for server_id, status in all_servers.items():
                if status.is_connected:
                    tools = mcp_manager.get_server_tools(server_id)
                    server_config = mcp_manager.get_server_config(server_id)
                    
                    if server_config:
                        # 将工具转换为字典格式
                        tool_dicts = []
                        for tool in tools:
                            if hasattr(tool, 'metadata') and tool.metadata:
                                tool_dicts.append({
                                    "name": tool.metadata.name,
                                    "description": tool.metadata.description,
                                    "parameters": getattr(tool.metadata, 'fn_schema', {})
                                })
                        
                        tool_responses.append(MCPToolResponse(
                            server_id=server_id,
                            server_name=server_config.name,
                            tools=tool_dicts
                        ))
            
            api_logger.info(f"从 {len(tool_responses)} 个 MCP 服务器检索到工具")
            return tool_responses
    except Exception as e:
        api_logger.error("无法获取所有 MCP 工具", exception=e)
        raise HTTPException(status_code=500, detail=f"获取所有 MCP 工具时出错: {str(e)}")
