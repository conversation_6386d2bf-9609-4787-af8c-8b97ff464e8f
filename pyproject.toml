[project]
name = "easy-agent-center"
version = "0.1.0"
description = ""
authors = [
    {name = "曹智勇",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.12,<4.0"
dependencies = [
    "fastapi (>=0.115.14,<0.116.0)",
    "uvicorn[standard] (>=0.35.0,<0.36.0)",
    "llama-index (>=0.12.48,<0.13.0)",
    "pytest (>=8.4.1,<9.0.0)",
    "aiohttp (>=3.12.13,<4.0.0)",
    "llama-index-llms-openai-like (>=0.4.0,<0.5.0)",
    "aiomysql (>=0.2.0,<0.3.0)",
    "pymysql (>=1.1.1,<2.0.0)",
    "sqlalchemy[asyncio] (>=2.0.41,<3.0.0)",
    "python-dotenv (>=1.1.1,<2.0.0)",
    "alembic (>=1.16.3,<2.0.0)",
    "llama-index-tools-mcp (>=0.2.6,<0.3.0)",
    "mcp (>=1.10.1,<2.0.0)",
]

[tool.poetry]
package-mode = false

[[tool.poetry.source]]
name = "tuna"
url = "https://pypi.tuna.tsinghua.edu.cn/simple"
priority = "primary"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 100
target-version = ['py312']
