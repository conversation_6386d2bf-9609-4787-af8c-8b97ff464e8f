"""Init

Revision ID: d9982628d637
Revises: 
Create Date: 2025-07-14 17:41:57.392650

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd9982628d637'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('agents',
    sa.<PERSON>umn('id', sa.Integer(), autoincrement=True, nullable=False, comment='主键 ID'),
    sa.<PERSON>umn('agent_id', sa.String(length=255), nullable=False, comment='Agent 的唯一标识符'),
    sa.Column('name', sa.String(length=255), nullable=False, comment='Agent 名称'),
    sa.Column('description', sa.Text(), nullable=True, comment='Agent 描述'),
    sa.Column('agent_type', sa.String(length=50), nullable=False, comment='Agent 类型，例如 react'),
    sa.Column('system_prompt', sa.Text(), nullable=True, comment='系统提示'),
    sa.Column('model', sa.String(length=255), nullable=False, comment='模型名称'),
    sa.Column('provider', sa.String(length=255), nullable=True, comment='LLM 提供商 ID'),
    sa.Column('is_default', sa.Boolean(), nullable=False, comment='是否为默认 Agent'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否激活'),
    sa.Column('tools', sa.JSON(), nullable=True, comment='工具列表 (JSON 数组)'),
    sa.Column('config', sa.JSON(), nullable=True, comment='其他配置 (JSON)'),
    sa.Column('llm_config', sa.JSON(), nullable=True, comment='LLM 的详细配置'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    comment='Agent 配置表'
    )
    op.create_index(op.f('ix_agents_agent_id'), 'agents', ['agent_id'], unique=True)
    op.create_table('chat_histories',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='主键 ID'),
    sa.Column('session_id', sa.String(length=255), nullable=False, comment='会话的唯一标识符'),
    sa.Column('agent_id', sa.String(length=255), nullable=False, comment='关联的 Agent ID'),
    sa.Column('title', sa.String(length=500), nullable=True, comment='可选的会话标题'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    comment='聊天会话历史表'
    )
    op.create_index(op.f('ix_chat_histories_agent_id'), 'chat_histories', ['agent_id'], unique=False)
    op.create_index(op.f('ix_chat_histories_session_id'), 'chat_histories', ['session_id'], unique=False)
    op.create_table('chat_messages',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='主键 ID'),
    sa.Column('chat_history_id', sa.Integer(), nullable=False, comment='关联的聊天历史 ID'),
    sa.Column('role', sa.String(length=20), nullable=False, comment='角色 (user, assistant, system)'),
    sa.Column('content', sa.Text(), nullable=False, comment='消息内容'),
    sa.Column('message_metadata', sa.JSON(), nullable=True, comment='其他消息元数据 (JSON)'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.PrimaryKeyConstraint('id'),
    comment='聊天消息表'
    )
    op.create_index(op.f('ix_chat_messages_chat_history_id'), 'chat_messages', ['chat_history_id'], unique=False)
    op.create_table('llm_providers',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='主键 ID'),
    sa.Column('provider_id', sa.String(length=255), nullable=False, comment='提供商的唯一标识符'),
    sa.Column('name', sa.String(length=255), nullable=False, comment='提供商名称'),
    sa.Column('description', sa.Text(), nullable=True, comment='提供商描述'),
    sa.Column('provider_type', sa.String(length=50), nullable=False, comment='提供商类型 (例如 openai, openai_compatible, azure_openai)'),
    sa.Column('api_base', sa.String(length=500), nullable=True, comment='API 地址'),
    sa.Column('api_key', sa.String(length=500), nullable=True, comment='API 密钥'),
    sa.Column('default_model', sa.String(length=255), nullable=False, comment='默认模型'),
    sa.Column('context_window', sa.Integer(), nullable=False, comment='上下文窗口大小'),
    sa.Column('max_tokens', sa.Integer(), nullable=True, comment='最大令牌数'),
    sa.Column('temperature', sa.Float(), nullable=False, comment='温度'),
    sa.Column('timeout', sa.Integer(), nullable=False, comment='超时时间 (秒)'),
    sa.Column('max_retries', sa.Integer(), nullable=False, comment='最大重试次数'),
    sa.Column('is_chat_model', sa.Boolean(), nullable=False, comment='是否为聊天模型'),
    sa.Column('is_function_calling_model', sa.Boolean(), nullable=False, comment='是否为函数调用模型'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否激活'),
    sa.Column('is_default', sa.Boolean(), nullable=False, comment='是否为默认提供商'),
    sa.Column('extra_params', sa.JSON(), nullable=True, comment='额外的提供商特定参数 (JSON)'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    comment='LLM 提供商配置表'
    )
    op.create_index(op.f('ix_llm_providers_provider_id'), 'llm_providers', ['provider_id'], unique=True)
    op.create_table('mcp_servers',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='主键 ID'),
    sa.Column('server_id', sa.String(length=255), nullable=False, comment='服务器的唯一标识符'),
    sa.Column('name', sa.String(length=255), nullable=False, comment='服务器名称'),
    sa.Column('type', sa.String(length=50), nullable=False, comment='服务器类型'),
    sa.Column('description', sa.Text(), nullable=True, comment='服务器描述'),
    sa.Column('server_url', sa.String(length=500), nullable=True, comment='SSE 端点 URL'),
    sa.Column('command', sa.String(length=500), nullable=True, comment='启动命令'),
    sa.Column('args', sa.JSON(), nullable=True, comment='命令参数 (JSON 列表)'),
    sa.Column('env', sa.JSON(), nullable=True, comment='环境变量 (JSON 字典)'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否激活'),
    sa.Column('timeout', sa.Integer(), nullable=False, comment='超时时间 (秒)'),
    sa.Column('max_retries', sa.Integer(), nullable=False, comment='最大重试次数'),
    sa.Column('extra_config', sa.JSON(), nullable=True, comment='额外的配置 (JSON)'),
    sa.Column('created_at', sa.DateTime(), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    comment='MCP 服务配置表'
    )
    op.create_index(op.f('ix_mcp_servers_name'), 'mcp_servers', ['name'], unique=False)
    op.create_index(op.f('ix_mcp_servers_server_id'), 'mcp_servers', ['server_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_mcp_servers_server_id'), table_name='mcp_servers')
    op.drop_index(op.f('ix_mcp_servers_name'), table_name='mcp_servers')
    op.drop_table('mcp_servers')
    op.drop_index(op.f('ix_llm_providers_provider_id'), table_name='llm_providers')
    op.drop_table('llm_providers')
    op.drop_index(op.f('ix_chat_messages_chat_history_id'), table_name='chat_messages')
    op.drop_table('chat_messages')
    op.drop_index(op.f('ix_chat_histories_session_id'), table_name='chat_histories')
    op.drop_index(op.f('ix_chat_histories_agent_id'), table_name='chat_histories')
    op.drop_table('chat_histories')
    op.drop_index(op.f('ix_agents_agent_id'), table_name='agents')
    op.drop_table('agents')
    # ### end Alembic commands ###
